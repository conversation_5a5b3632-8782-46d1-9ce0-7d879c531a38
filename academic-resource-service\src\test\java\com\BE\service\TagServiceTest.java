package com.BE.service;

import com.BE.exception.AcademicResourceException;
import com.BE.exception.ResourceNotFoundException;
import com.BE.model.Tag;
import com.BE.model.request.TagCreateRequest;
import com.BE.model.response.TagResponse;
import com.BE.repository.ResourceTagRepository;
import com.BE.repository.TagRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TagServiceTest {

    @Mock
    private TagRepository tagRepository;

    @Mock
    private ResourceTagRepository resourceTagRepository;

    @InjectMocks
    private TagService tagService;

    private Tag testTag;
    private TagCreateRequest createRequest;

    @BeforeEach
    void setUp() {
        testTag = new Tag();
        testTag.setId(1L);
        testTag.setName("Mathematics");
        testTag.setDescription("Math related resources");

        createRequest = new TagCreateRequest();
        createRequest.setName("Mathematics");
        createRequest.setDescription("Math related resources");
    }

    @Test
    void createTag_Success() {
        // Given
        when(tagRepository.existsByNameIgnoreCase(anyString())).thenReturn(false);
        when(tagRepository.save(any(Tag.class))).thenReturn(testTag);

        // When
        TagResponse response = tagService.createTag(createRequest);

        // Then
        assertNotNull(response);
        assertEquals(testTag.getId(), response.getId());
        assertEquals(testTag.getName(), response.getName());
        assertEquals(testTag.getDescription(), response.getDescription());
        verify(tagRepository).save(any(Tag.class));
    }

    @Test
    void createTag_AlreadyExists() {
        // Given
        when(tagRepository.existsByNameIgnoreCase(anyString())).thenReturn(true);

        // When & Then
        assertThrows(AcademicResourceException.class, 
                () -> tagService.createTag(createRequest));
        verify(tagRepository, never()).save(any());
    }

    @Test
    void getTagById_Success() {
        // Given
        when(tagRepository.findById(1L)).thenReturn(Optional.of(testTag));

        // When
        TagResponse response = tagService.getTagById(1L);

        // Then
        assertNotNull(response);
        assertEquals(testTag.getId(), response.getId());
        assertEquals(testTag.getName(), response.getName());
        verify(tagRepository).findById(1L);
    }

    @Test
    void getTagById_NotFound() {
        // Given
        when(tagRepository.findById(anyLong())).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, 
                () -> tagService.getTagById(999L));
        verify(tagRepository).findById(999L);
    }

    @Test
    void deleteTag_Success() {
        // Given
        when(tagRepository.findById(1L)).thenReturn(Optional.of(testTag));
        when(resourceTagRepository.countByTagId(1L)).thenReturn(0L);

        // When
        tagService.deleteTag(1L);

        // Then
        verify(tagRepository).findById(1L);
        verify(resourceTagRepository).countByTagId(1L);
        verify(tagRepository).delete(testTag);
    }

    @Test
    void deleteTag_InUse() {
        // Given
        when(tagRepository.findById(1L)).thenReturn(Optional.of(testTag));
        when(resourceTagRepository.countByTagId(1L)).thenReturn(5L);

        // When & Then
        assertThrows(AcademicResourceException.class, 
                () -> tagService.deleteTag(1L));
        verify(tagRepository).findById(1L);
        verify(resourceTagRepository).countByTagId(1L);
        verify(tagRepository, never()).delete(any());
    }

    @Test
    void deleteTag_NotFound() {
        // Given
        when(tagRepository.findById(anyLong())).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, 
                () -> tagService.deleteTag(999L));
        verify(tagRepository).findById(999L);
        verify(tagRepository, never()).delete(any());
    }
}
