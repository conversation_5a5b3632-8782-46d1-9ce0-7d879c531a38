# Academic Resource Management System

Hệ thống quản lý học liệu với các tính năng CRUD, quản lý tag, tìm kiếm và phân trang, tích hợp Supabase Storage.

## Tính năng chính

### 1. <PERSON><PERSON><PERSON><PERSON> lý học liệu (Academic Resource CRUD)
- ✅ Tạo học liệu mới với metadata
- ✅ Upload file lên Supabase Storage
- ✅ Xem chi tiết học liệu
- ✅ Cập nhật thông tin học liệu
- ✅ X<PERSON>a học liệu (bao gồm file trên storage)

### 2. Quản lý Tag
- ✅ Tạo, sửa, xóa tag
- ✅ G<PERSON> nhiều tag cho một học liệu
- ✅ Tìm kiếm tag
- ✅ Xem tag phổ biến
- ✅ Xem tag chưa sử dụng

### 3. Tìm kiếm và Lọc
- ✅ Tìm kiếm theo tên/mô tả
- ✅ <PERSON><PERSON><PERSON> theo lo<PERSON> (image, gif, video, webp, iframe)
- ✅ Lọc theo tag
- ✅ Lọc theo người tạo
- ✅ Phân trang và sắp xếp

### 4. Quản lý File
- ✅ Upload file lên Supabase Storage
- ✅ Tự động tạo URL công khai
- ✅ Xóa file khi xóa học liệu

## Cấu trúc Database

### Bảng `academic_resource`
```sql
CREATE TABLE academic_resource (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    type VARCHAR(20) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    url TEXT NOT NULL,
    created_by BIGINT NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

### Bảng `tag`
```sql
CREATE TABLE tag (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT
);
```

### Bảng `resource_tag`
```sql
CREATE TABLE resource_tag (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    resource_id BIGINT NOT NULL,
    tag_id BIGINT NOT NULL,
    FOREIGN KEY (resource_id) REFERENCES academic_resource(id),
    FOREIGN KEY (tag_id) REFERENCES tag(id)
);
```

## API Endpoints

### Academic Resource Management

#### 1. Tạo học liệu
```http
POST /api/academic-resources
Content-Type: application/json

{
    "type": "image",
    "name": "Math Formula Collection",
    "description": "Collection of important math formulas",
    "url": "https://example.com/resource.pdf",
    "tagIds": [1, 2, 3]
}
```

#### 2. Tạo học liệu với upload file
```http
POST /api/academic-resources/upload
Content-Type: multipart/form-data

resource: {JSON metadata}
file: {binary file}
```

#### 3. Tìm kiếm học liệu
```http
GET /api/academic-resources/search?keyword=math&type=image&page=0&size=10
```

#### 4. Tìm kiếm nâng cao
```http
POST /api/academic-resources/search
Content-Type: application/json

{
    "keyword": "math",
    "type": "image",
    "tagIds": [1, 2],
    "createdBy": 123,
    "page": 0,
    "size": 10,
    "sortBy": "createdAt",
    "sortDirection": "desc"
}
```

### Tag Management

#### 1. Tạo tag
```http
POST /api/tags
Content-Type: application/json

{
    "name": "Mathematics",
    "description": "Resources related to mathematics"
}
```

#### 2. Lấy tất cả tag
```http
GET /api/tags
```

#### 3. Tìm kiếm tag
```http
GET /api/tags/search?keyword=math&page=0&size=10
```

#### 4. Xem tag phổ biến
```http
GET /api/tags/popular?page=0&size=10
```

## Cấu hình

### Application Properties
```properties
# Database
spring.datasource.url=**************************************
spring.datasource.username=root
spring.datasource.password=your_password

# Supabase Storage
supabase.url=https://zzfjygzmhvvsvycmvrlm.supabase.co
supabase.key=your_supabase_key
supabase.storage.bucket=planbookai-academic-resource

# File Upload
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=100MB
```

## Chạy ứng dụng

1. **Cài đặt dependencies:**
```bash
mvn clean install
```

2. **Chạy ứng dụng:**
```bash
mvn spring-boot:run
```

3. **Truy cập Swagger UI:**
```
http://localhost:8080/swagger-ui/index.html
```

## Testing

Chạy unit tests:
```bash
mvn test
```

## Cấu trúc thư mục

```
src/main/java/com/BE/
├── config/
│   ├── SecurityConfig.java
│   └── SupabaseConfig.java
├── controller/
│   ├── AcademicResourceController.java
│   └── TagController.java
├── exception/
│   ├── AcademicResourceException.java
│   ├── FileUploadException.java
│   ├── ResourceNotFoundException.java
│   └── GlobalExceptionHandler.java
├── model/
│   ├── AcademicResource.java
│   ├── Tag.java
│   ├── ResourceTag.java
│   ├── request/
│   └── response/
├── repository/
│   ├── AcademicResourceRepository.java
│   ├── TagRepository.java
│   └── ResourceTagRepository.java
├── service/
│   ├── AcademicResourceService.java
│   ├── TagService.java
│   └── SupabaseStorageService.java
└── utils/
    ├── AccountUtils.java
    └── ResponseHandler.java
```

## Lưu ý

1. **File Types hỗ trợ:** image, gif, video, webp, iframe
2. **File size tối đa:** 100MB
3. **Authentication:** Sử dụng header-based authentication
4. **Storage:** Files được lưu trên Supabase Storage
5. **Database:** MySQL với JPA/Hibernate
