package com.BE.service;

import com.BE.exception.ResourceNotFoundException;
import com.BE.model.AcademicResource;
import com.BE.model.request.AcademicResourceCreateRequest;
import com.BE.model.response.AcademicResourceResponse;
import com.BE.repository.AcademicResourceRepository;
import com.BE.repository.ResourceTagRepository;
import com.BE.repository.TagRepository;
import com.BE.utils.AccountUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AcademicResourceServiceTest {

    @Mock
    private AcademicResourceRepository academicResourceRepository;

    @Mock
    private TagRepository tagRepository;

    @Mock
    private ResourceTagRepository resourceTagRepository;

    @Mock
    private SupabaseStorageService supabaseStorageService;

    @Mock
    private AccountUtils accountUtils;

    @InjectMocks
    private AcademicResourceService academicResourceService;

    private AcademicResource testResource;
    private AcademicResourceCreateRequest createRequest;

    @BeforeEach
    void setUp() {
        testResource = new AcademicResource();
        testResource.setId(1L);
        testResource.setType("image");
        testResource.setName("Test Resource");
        testResource.setDescription("Test Description");
        testResource.setUrl("https://example.com/test.jpg");
        testResource.setCreatedBy(123L);
        testResource.setCreatedAt(LocalDateTime.now());
        testResource.setUpdatedAt(LocalDateTime.now());
        testResource.setResourceTags(new HashSet<>());

        createRequest = new AcademicResourceCreateRequest();
        createRequest.setType("image");
        createRequest.setName("Test Resource");
        createRequest.setDescription("Test Description");
        createRequest.setUrl("https://example.com/test.jpg");
        createRequest.setTagIds(new HashSet<>());
    }

    @Test
    void createResource_Success() {
        // Given
        when(accountUtils.getCurrentUserId()).thenReturn("123");
        when(academicResourceRepository.save(any(AcademicResource.class))).thenReturn(testResource);

        // When
        AcademicResourceResponse response = academicResourceService.createResource(createRequest);

        // Then
        assertNotNull(response);
        assertEquals(testResource.getId(), response.getId());
        assertEquals(testResource.getName(), response.getName());
        assertEquals(testResource.getType(), response.getType());
        verify(academicResourceRepository).save(any(AcademicResource.class));
    }

    @Test
    void getResourceById_Success() {
        // Given
        when(academicResourceRepository.findById(1L)).thenReturn(Optional.of(testResource));

        // When
        AcademicResourceResponse response = academicResourceService.getResourceById(1L);

        // Then
        assertNotNull(response);
        assertEquals(testResource.getId(), response.getId());
        assertEquals(testResource.getName(), response.getName());
        verify(academicResourceRepository).findById(1L);
    }

    @Test
    void getResourceById_NotFound() {
        // Given
        when(academicResourceRepository.findById(anyLong())).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, 
                () -> academicResourceService.getResourceById(999L));
        verify(academicResourceRepository).findById(999L);
    }

    @Test
    void deleteResource_Success() {
        // Given
        when(academicResourceRepository.findById(1L)).thenReturn(Optional.of(testResource));
        when(supabaseStorageService.extractFileNameFromUrl(anyString())).thenReturn("test-file.jpg");
        when(supabaseStorageService.deleteFile(anyString())).thenReturn(true);

        // When
        academicResourceService.deleteResource(1L);

        // Then
        verify(academicResourceRepository).findById(1L);
        verify(academicResourceRepository).delete(testResource);
        verify(supabaseStorageService).deleteFile("test-file.jpg");
    }

    @Test
    void deleteResource_NotFound() {
        // Given
        when(academicResourceRepository.findById(anyLong())).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, 
                () -> academicResourceService.deleteResource(999L));
        verify(academicResourceRepository).findById(999L);
        verify(academicResourceRepository, never()).delete(any());
    }
}
